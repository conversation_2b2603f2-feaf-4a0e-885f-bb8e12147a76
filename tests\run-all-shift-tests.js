/**
 * Master Test Runner: Shift Management System Enhancement Tests
 * Purpose: Execute all test suites for the five shift management issues
 * 
 * Test Suites:
 * 1. Automatic Shift Transitions (ISSUE 1)
 * 2. Custom Shift Display Labels (ISSUE 2)
 * 3. Date Range Scheduling (ISSUE 3)
 * 4. Edit Functionality (ISSUE 4)
 * 5. Trip Status Terminology (ISSUE 5)
 * 6. Custom Shift Intelligence (ENHANCEMENT)
 * 
 * Usage:
 * node tests/run-all-shift-tests.js
 * 
 * Requirements:
 * - PostgreSQL running on localhost:5432
 * - Database user: postgres
 * - Database password: PostgreSQLPassword
 * - Backend running on port 5000
 * - Frontend running on port 3000
 */

const ShiftTransitionTester = require('./shift-automatic-transitions.test.js');
const ShiftDisplayLabelTester = require('./shift-display-labels.test.js');
const DateRangeSchedulingTester = require('./shift-date-range-scheduling.test.js');
const ShiftEditFunctionalityTester = require('./shift-edit-functionality.test.js');
const TripStatusTerminologyTester = require('./trip-status-terminology.test.js');
const CustomShiftIntelligenceTester = require('./custom-shift-intelligence.test.js');

class MasterShiftTestRunner {
  constructor() {
    this.testSuites = [
      {
        name: 'Automatic Shift Transitions',
        tester: ShiftTransitionTester,
        issue: 'ISSUE 1',
        description: 'Missing automatic shift status transition system',
        requiresDatabase: true
      },
      {
        name: 'Custom Shift Display Labels',
        tester: ShiftDisplayLabelTester,
        issue: 'ISSUE 2',
        description: 'Incorrect custom shift display labels in TripsTable.js',
        requiresDatabase: false
      },
      {
        name: 'Date Range Scheduling',
        tester: DateRangeSchedulingTester,
        issue: 'ISSUE 3',
        description: 'Enhanced shift scheduling with date ranges and recurrence',
        requiresDatabase: true
      },
      {
        name: 'Edit Functionality',
        tester: ShiftEditFunctionalityTester,
        issue: 'ISSUE 4',
        description: 'Broken update/edit functionality in Shift Management table',
        requiresDatabase: false
      },
      {
        name: 'Trip Status Terminology',
        tester: TripStatusTerminologyTester,
        issue: 'ISSUE 5',
        description: 'Update trip_status enum from breakdown to stopped',
        requiresDatabase: true
      },
      {
        name: 'Custom Shift Intelligence',
        tester: CustomShiftIntelligenceTester,
        issue: 'ENHANCEMENT',
        description: 'Intelligent custom shift time classification',
        requiresDatabase: false
      }
    ];

    this.results = [];
  }

  async checkPrerequisites() {
    console.log('🔍 Checking Prerequisites...\n');

    const checks = [
      {
        name: 'PostgreSQL Connection',
        check: async () => {
          try {
            const { Pool } = require('pg');
            const pool = new Pool({
              host: 'localhost',
              port: 5432,
              database: 'postgres',
              user: 'postgres',
              password: 'PostgreSQLPassword'
            });
            await pool.query('SELECT 1');
            await pool.end();
            return true;
          } catch (error) {
            console.log(`   ❌ PostgreSQL connection failed: ${error.message}`);
            return false;
          }
        }
      },
      {
        name: 'Node.js Dependencies',
        check: async () => {
          try {
            require('pg');
            require('assert');
            return true;
          } catch (error) {
            console.log(`   ❌ Missing dependencies: ${error.message}`);
            return false;
          }
        }
      }
    ];

    let allPassed = true;

    for (const check of checks) {
      const passed = await check.check();
      console.log(`${passed ? '✅' : '❌'} ${check.name}`);
      if (!passed) allPassed = false;
    }

    if (!allPassed) {
      console.log('\n⚠️  Prerequisites not met. Please ensure:');
      console.log('   1. PostgreSQL is running on localhost:5432');
      console.log('   2. Database user "postgres" with password "PostgreSQLPassword"');
      console.log('   3. Required Node.js packages are installed');
      console.log('\nInstall dependencies: npm install pg');
      return false;
    }

    console.log('\n✅ All prerequisites met!\n');
    return true;
  }

  async runTestSuite(testSuite) {
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🧪 RUNNING: ${testSuite.name} (${testSuite.issue})`);
    console.log(`📝 ${testSuite.description}`);
    console.log(`🗄️  Database Required: ${testSuite.requiresDatabase ? 'Yes' : 'No'}`);
    console.log(`${'='.repeat(80)}`);

    const startTime = Date.now();
    let success = false;
    let error = null;

    try {
      const tester = new testSuite.tester();
      
      if (typeof tester.runAllTests === 'function') {
        await tester.runAllTests();
        success = true;
      } else {
        throw new Error('Test suite does not have runAllTests method');
      }
    } catch (err) {
      error = err;
      console.error(`❌ Test suite failed: ${err.message}`);
    }

    const duration = Date.now() - startTime;

    const result = {
      name: testSuite.name,
      issue: testSuite.issue,
      success,
      error: error?.message,
      duration,
      requiresDatabase: testSuite.requiresDatabase
    };

    this.results.push(result);
    return result;
  }

  async runAllTests() {
    console.log('🚀 SHIFT MANAGEMENT SYSTEM ENHANCEMENT - MASTER TEST RUNNER');
    console.log('============================================================\n');

    // Check prerequisites
    const prerequisitesPassed = await this.checkPrerequisites();
    if (!prerequisitesPassed) {
      return;
    }

    console.log('🎯 Test-Driven Development Approach');
    console.log('📋 Running comprehensive validation before implementation\n');

    const startTime = Date.now();
    let totalPassed = 0;

    // Run each test suite
    for (const testSuite of this.testSuites) {
      const result = await this.runTestSuite(testSuite);
      if (result.success) {
        totalPassed++;
      }

      // Add delay between test suites for database cleanup
      if (testSuite.requiresDatabase) {
        console.log('\n⏳ Waiting for database cleanup...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const totalDuration = Date.now() - startTime;

    // Generate comprehensive report
    this.generateFinalReport(totalPassed, totalDuration);
  }

  generateFinalReport(totalPassed, totalDuration) {
    console.log('\n\n' + '='.repeat(80));
    console.log('📊 COMPREHENSIVE TEST RESULTS SUMMARY');
    console.log('='.repeat(80));

    // Individual test suite results
    this.results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const duration = `${result.duration}ms`;
      console.log(`${index + 1}. ${status} ${result.name} (${result.issue}) - ${duration}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    console.log('\n' + '-'.repeat(80));

    // Overall statistics
    const totalTests = this.testSuites.length;
    const passRate = ((totalPassed / totalTests) * 100).toFixed(1);
    
    console.log(`🎯 OVERALL RESULTS: ${totalPassed}/${totalTests} test suites passed (${passRate}%)`);
    console.log(`⏱️  TOTAL DURATION: ${totalDuration}ms (${(totalDuration / 1000).toFixed(1)}s)`);

    // Database vs Non-Database tests
    const dbTests = this.results.filter(r => r.requiresDatabase);
    const nonDbTests = this.results.filter(r => !r.requiresDatabase);
    const dbPassed = dbTests.filter(r => r.success).length;
    const nonDbPassed = nonDbTests.filter(r => r.success).length;

    console.log(`🗄️  DATABASE TESTS: ${dbPassed}/${dbTests.length} passed`);
    console.log(`🖥️  FRONTEND TESTS: ${nonDbPassed}/${nonDbTests.length} passed`);

    console.log('\n' + '-'.repeat(80));

    // Implementation readiness assessment
    if (totalPassed === totalTests) {
      console.log('🎉 ALL TESTS PASSED - READY FOR IMPLEMENTATION!');
      console.log('\n✅ Implementation Order:');
      console.log('   1. Phase 2: Database Schema Enhancements');
      console.log('   2. Phase 3: Automatic Shift Status Transition System');
      console.log('   3. Phase 4: Frontend Display Label Corrections');
      console.log('   4. Phase 5: Enhanced Shift Scheduling with Date Ranges');
      console.log('   5. Phase 6: Shift Management Table Edit Functionality Fix');
      console.log('   6. Phase 7: Trip Status Terminology Update');
      console.log('   7. Phase 8: Integration Testing and Validation');
      console.log('   8. Phase 9: Cleanup and Documentation');
      
      console.log('\n🔧 Key Implementation Points:');
      console.log('   • Maintain 100% backward compatibility');
      console.log('   • Preserve 4-phase workflow integrity');
      console.log('   • Keep driver history functionality intact');
      console.log('   • Ensure <300ms performance targets');
      console.log('   • Test on ports: Backend 5000, Frontend 3000');
    } else {
      console.log('⚠️  SOME TESTS FAILED - REVIEW BEFORE IMPLEMENTATION');
      console.log('\n❌ Failed Test Suites:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`   • ${result.name} (${result.issue}): ${result.error}`);
      });
      
      console.log('\n🔧 Recommended Actions:');
      console.log('   1. Review failed test cases');
      console.log('   2. Fix any prerequisite issues');
      console.log('   3. Re-run tests until all pass');
      console.log('   4. Only proceed with implementation after 100% pass rate');
    }

    console.log('\n' + '='.repeat(80));
    console.log('🏁 TEST EXECUTION COMPLETE');
    console.log('='.repeat(80));
  }
}

// Run master test suite if this file is executed directly
if (require.main === module) {
  const runner = new MasterShiftTestRunner();
  runner.runAllTests().catch(error => {
    console.error('❌ Master test runner failed:', error);
    process.exit(1);
  });
}

module.exports = MasterShiftTestRunner;
