-- Migration: Analytics & Reports System Query Optimization
-- Description: Add specialized indexes and materialized views for Analytics & Reports system performance
-- Version: 014
-- Date: 2025-01-07

-- ============================================================================
-- ANALYTICS-SPECIFIC INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Fleet overview analytics indexes
DO $$
BEGIN
    -- Index for fleet status queries (active trucks, current operations)
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trips_current_status_time'
    ) THEN
        CREATE INDEX idx_trips_current_status_time 
        ON trip_logs(status, created_at DESC) 
        WHERE status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'breakdown');
    END IF;

    -- Index for breakdown analytics
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trips_breakdown_analytics'
    ) THEN
        CREATE INDEX idx_trips_breakdown_analytics 
        ON trip_logs(status, breakdown_reported_at, breakdown_resolved_at, previous_status) 
        WHERE status = 'breakdown';
    END IF;

    -- Index for phase duration analytics
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trips_phase_durations'
    ) THEN
        CREATE INDEX idx_trips_phase_durations 
        ON trip_logs(status, loading_duration_minutes, travel_duration_minutes, unloading_duration_minutes, created_at) 
        WHERE status = 'trip_completed' AND total_duration_minutes IS NOT NULL;
    END IF;

    -- Index for live operations queries
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_assignments_active_operations'
    ) THEN
        CREATE INDEX idx_assignments_active_operations 
        ON assignments(truck_id, status, created_at) 
        WHERE status IN ('assigned', 'in_progress');
    END IF;

    -- Index for location-based performance analytics
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trips_location_performance'
    ) THEN
        CREATE INDEX idx_trips_location_performance 
        ON trip_logs(actual_loading_location_id, actual_unloading_location_id, status, created_at, total_duration_minutes);
    END IF;

    -- Index for time-based analytics (hourly/daily patterns)
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_trips_time_analytics'
    ) THEN
        CREATE INDEX idx_trips_time_analytics 
        ON trip_logs(DATE(created_at), EXTRACT(HOUR FROM created_at), status);
    END IF;
END $$;

-- ============================================================================
-- MATERIALIZED VIEW FOR REAL-TIME FLEET STATUS
-- ============================================================================

-- Drop existing view if it exists
DROP MATERIALIZED VIEW IF EXISTS mv_fleet_status_summary;

-- Create materialized view for fleet status analytics
CREATE MATERIALIZED VIEW mv_fleet_status_summary AS
SELECT
  dt.id as truck_id,
  dt.truck_number,
  dt.status as truck_status,
  d.full_name as driver_name,
  a.assignment_code,
  a.status as assignment_status,
  
  -- Current trip information
  tl.status as current_trip_status,
  tl.trip_number,
  tl.created_at as trip_started_at,
  
  -- Current locations
  COALESCE(al.name, ll.name) as current_loading_location,
  COALESCE(aul.name, ul.name) as current_unloading_location,
  
  -- Time tracking
  CASE 
    WHEN tl.status = 'loading_start' AND tl.loading_start_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.loading_start_time))/60
    WHEN tl.status = 'loading_end' AND tl.loading_end_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.loading_end_time))/60
    WHEN tl.status = 'unloading_start' AND tl.unloading_start_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.unloading_start_time))/60
    WHEN tl.status = 'unloading_end' AND tl.unloading_end_time IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.unloading_end_time))/60
    WHEN tl.status = 'breakdown' AND tl.breakdown_reported_at IS NOT NULL THEN
      EXTRACT(EPOCH FROM (NOW() - tl.breakdown_reported_at))/60
    ELSE 0
  END as time_in_current_phase_minutes,
  
  -- Performance indicators
  tl.total_duration_minutes,
  tl.is_exception,
  tl.breakdown_reason,
  a.priority,
  a.is_adaptive,
  
  -- Update timestamp
  NOW() as last_updated

FROM dump_trucks dt
LEFT JOIN assignments a ON dt.id = a.truck_id 
  AND a.status IN ('assigned', 'in_progress')
LEFT JOIN drivers d ON a.driver_id = d.id
LEFT JOIN trip_logs tl ON a.id = tl.assignment_id 
  AND tl.id = (
    SELECT id FROM trip_logs tl2 
    WHERE tl2.assignment_id = a.id 
    ORDER BY tl2.created_at DESC 
    LIMIT 1
  )
LEFT JOIN locations ll ON a.loading_location_id = ll.id
LEFT JOIN locations ul ON a.unloading_location_id = ul.id
LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
WHERE dt.status = 'active';

-- Create index on materialized view (non-unique due to potential NULL values)
CREATE INDEX idx_mv_fleet_status_truck_id
ON mv_fleet_status_summary(truck_id);

CREATE INDEX idx_mv_fleet_status_trip_status
ON mv_fleet_status_summary(current_trip_status, truck_number);

-- ============================================================================
-- MATERIALIZED VIEW FOR BREAKDOWN ANALYTICS
-- ============================================================================

-- Drop existing view if it exists
DROP MATERIALIZED VIEW IF EXISTS mv_breakdown_analytics_summary;

-- Create materialized view for breakdown analytics
CREATE MATERIALIZED VIEW mv_breakdown_analytics_summary AS
SELECT
  dt.truck_number,
  dt.id as truck_id,
  d.full_name as driver_name,
  
  -- Breakdown counts and timing
  COUNT(*) as total_breakdowns,
  AVG(EXTRACT(EPOCH FROM (tl.breakdown_resolved_at - tl.breakdown_reported_at))/60) as avg_resolution_time_minutes,
  MAX(tl.breakdown_reported_at) as last_breakdown_date,
  
  -- Breakdown by phase
  COUNT(CASE WHEN tl.previous_status = 'loading_start' THEN 1 END) as loading_phase_breakdowns,
  COUNT(CASE WHEN tl.previous_status = 'loading_end' THEN 1 END) as travel_to_unload_breakdowns,
  COUNT(CASE WHEN tl.previous_status = 'unloading_start' THEN 1 END) as unloading_phase_breakdowns,
  COUNT(CASE WHEN tl.previous_status = 'unloading_end' THEN 1 END) as travel_to_load_breakdowns,
  
  -- Breakdown reasons
  MODE() WITHIN GROUP (ORDER BY tl.breakdown_reason) as most_common_reason,
  COUNT(DISTINCT tl.breakdown_reason) as unique_breakdown_reasons,
  
  -- Time patterns
  MODE() WITHIN GROUP (ORDER BY EXTRACT(HOUR FROM tl.breakdown_reported_at)) as most_common_hour,
  MODE() WITHIN GROUP (ORDER BY EXTRACT(DOW FROM tl.breakdown_reported_at)) as most_common_day_of_week,
  
  -- Performance impact
  AVG(tl.total_duration_minutes) as avg_trip_duration_with_breakdown,
  
  -- Update timestamp
  NOW() as last_updated

FROM trip_logs tl
JOIN assignments a ON tl.assignment_id = a.id
JOIN dump_trucks dt ON a.truck_id = dt.id
LEFT JOIN drivers d ON a.driver_id = d.id
WHERE tl.status = 'breakdown'
  AND tl.breakdown_reported_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY dt.truck_number, dt.id, d.full_name;

-- Create index on breakdown analytics materialized view (non-unique due to potential NULL values)
CREATE INDEX idx_mv_breakdown_analytics_truck_id
ON mv_breakdown_analytics_summary(truck_id);

CREATE INDEX idx_mv_breakdown_analytics_count
ON mv_breakdown_analytics_summary(total_breakdowns DESC, avg_resolution_time_minutes DESC);

-- ============================================================================
-- REFRESH FUNCTIONS FOR MATERIALIZED VIEWS
-- ============================================================================

-- Function to refresh fleet status materialized view
CREATE OR REPLACE FUNCTION refresh_fleet_status_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh breakdown analytics materialized view
CREATE OR REPLACE FUNCTION refresh_breakdown_analytics_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh all analytics materialized views
CREATE OR REPLACE FUNCTION refresh_all_analytics_views()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_trip_performance_summary;
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
  REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SCHEDULED REFRESH (Optional - can be set up with pg_cron if available)
-- ============================================================================

-- Add comments for documentation
COMMENT ON MATERIALIZED VIEW mv_fleet_status_summary IS 'Real-time fleet status for Analytics & Reports dashboard';
COMMENT ON MATERIALIZED VIEW mv_breakdown_analytics_summary IS 'Breakdown analytics summary for performance analysis';
COMMENT ON FUNCTION refresh_all_analytics_views() IS 'Refresh all analytics materialized views for optimal performance';

-- ============================================================================
-- PERFORMANCE MONITORING QUERIES
-- ============================================================================

-- Query to check index usage for analytics
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- WHERE indexname LIKE 'idx_%analytics%' OR indexname LIKE 'idx_mv_%'
-- ORDER BY idx_scan DESC;

-- Query to monitor materialized view refresh performance
-- SELECT schemaname, matviewname, n_tup_ins, n_tup_upd, n_tup_del 
-- FROM pg_stat_user_tables 
-- WHERE relname LIKE 'mv_%summary'
-- ORDER BY n_tup_ins DESC;
