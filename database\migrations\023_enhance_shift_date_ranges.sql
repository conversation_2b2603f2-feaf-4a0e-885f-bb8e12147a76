-- ============================================================================
-- Migration 023: Enhanced Shift Scheduling with Date Ranges
-- Purpose: Add date range support and recurrence patterns to driver_shifts
-- Issues Addressed: ISSUE 3 - Enhanced Shift Scheduling with Date Ranges
-- Date: 2025-01-09
-- ============================================================================

-- Create recurrence pattern enum
DO $$ BEGIN
    CREATE TYPE recurrence_pattern AS ENUM ('single', 'daily', 'weekly', 'weekdays', 'weekends', 'custom');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add new fields to driver_shifts table
ALTER TABLE driver_shifts 
ADD COLUMN IF NOT EXISTS start_date DATE,
ADD COLUMN IF NOT EXISTS end_date DATE,
ADD COLUMN IF NOT EXISTS recurrence_pattern recurrence_pattern NOT NULL DEFAULT 'single',
ADD COLUMN IF NOT EXISTS display_type shift_type; -- For intelligent shift classification

-- Create index for efficient date range queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_date_range 
ON driver_shifts (start_date, end_date, recurrence_pattern);

CREATE INDEX IF NOT EXISTS idx_driver_shifts_recurrence_active 
ON driver_shifts (recurrence_pattern, status) 
WHERE status IN ('scheduled', 'active');

-- Add constraint to ensure valid date configurations
ALTER TABLE driver_shifts 
ADD CONSTRAINT valid_shift_date_config CHECK (
    (recurrence_pattern = 'single' AND shift_date IS NOT NULL) OR
    (recurrence_pattern != 'single' AND start_date IS NOT NULL AND end_date IS NOT NULL AND start_date <= end_date)
);

-- Migrate existing single-date shifts to new structure
UPDATE driver_shifts 
SET 
    start_date = shift_date,
    end_date = shift_date,
    recurrence_pattern = 'single',
    display_type = shift_type
WHERE recurrence_pattern = 'single' 
  AND start_date IS NULL 
  AND shift_date IS NOT NULL;

-- ============================================================================
-- Enhanced Functions for Date Range Support
-- ============================================================================

-- Function to check if a shift is active on a given date
CREATE OR REPLACE FUNCTION is_shift_active_on_date(
    p_shift_id INTEGER,
    p_check_date DATE DEFAULT CURRENT_DATE
) RETURNS BOOLEAN AS $$
DECLARE
    v_shift RECORD;
    v_is_active BOOLEAN := FALSE;
BEGIN
    -- Get shift details
    SELECT 
        shift_date, start_date, end_date, recurrence_pattern,
        EXTRACT(DOW FROM start_date) as start_dow
    INTO v_shift
    FROM driver_shifts 
    WHERE id = p_shift_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check based on recurrence pattern
    CASE v_shift.recurrence_pattern
        WHEN 'single' THEN
            v_is_active := (v_shift.shift_date = p_check_date);
            
        WHEN 'daily' THEN
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        WHEN 'weekly' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) = v_shift.start_dow
            );
            
        WHEN 'weekdays' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) BETWEEN 1 AND 5
            );
            
        WHEN 'weekends' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) IN (0, 6)
            );
            
        WHEN 'custom' THEN
            -- Custom patterns handled by application logic
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        ELSE
            v_is_active := FALSE;
    END CASE;
    
    RETURN v_is_active;
END;
$$ LANGUAGE plpgsql;

-- Enhanced function to get current driver for truck with date range support
CREATE OR REPLACE FUNCTION get_current_driver_for_truck_enhanced(
    p_truck_id INTEGER,
    p_check_date DATE DEFAULT CURRENT_DATE,
    p_check_time TIME DEFAULT CURRENT_TIME
) RETURNS TABLE (
    driver_id INTEGER,
    driver_name VARCHAR(100),
    employee_id VARCHAR(20),
    shift_type shift_type,
    display_type shift_type,
    shift_id INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.id as shift_id
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND is_shift_active_on_date(ds.id, p_check_date)
      AND p_check_time BETWEEN ds.start_time AND 
          CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
          END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to get all active shifts for a date with recurrence support
CREATE OR REPLACE FUNCTION get_active_shifts_for_date(
    p_check_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE (
    shift_id INTEGER,
    truck_id INTEGER,
    driver_id INTEGER,
    shift_type shift_type,
    display_type shift_type,
    recurrence_pattern recurrence_pattern,
    start_time TIME,
    end_time TIME,
    status shift_status
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id as shift_id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.recurrence_pattern,
        ds.start_time,
        ds.end_time,
        ds.status
    FROM driver_shifts ds
    WHERE ds.status IN ('scheduled', 'active')
      AND (
        -- Single date shifts (backward compatibility)
        (ds.recurrence_pattern = 'single' AND ds.shift_date = p_check_date)
        OR
        -- Date range shifts with recurrence patterns
        (ds.recurrence_pattern != 'single' AND is_shift_active_on_date(ds.id, p_check_date))
      )
    ORDER BY ds.truck_id, ds.start_time;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Enhanced Automatic Shift Transition Functions
-- ============================================================================

-- Enhanced auto-activation function with date range support
CREATE OR REPLACE FUNCTION auto_activate_shifts_enhanced()
RETURNS INTEGER AS $$
DECLARE
    v_activated_count INTEGER := 0;
    v_shift_record RECORD;
BEGIN
    -- Get all shifts that should be activated now
    FOR v_shift_record IN
        SELECT ds.id, ds.truck_id
        FROM driver_shifts ds
        WHERE ds.status = 'scheduled'
          AND is_shift_active_on_date(ds.id, CURRENT_DATE)
          AND CURRENT_TIME >= ds.start_time
          AND CURRENT_TIME < CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
          END
    LOOP
        -- Deactivate any currently active shifts for this truck
        UPDATE driver_shifts 
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE truck_id = v_shift_record.truck_id 
          AND status = 'active'
          AND id != v_shift_record.id;
        
        -- Activate the new shift
        UPDATE driver_shifts 
        SET status = 'active', updated_at = CURRENT_TIMESTAMP
        WHERE id = v_shift_record.id;
        
        v_activated_count := v_activated_count + 1;
    END LOOP;
    
    RETURN v_activated_count;
END;
$$ LANGUAGE plpgsql;

-- Enhanced auto-completion function with date range support
CREATE OR REPLACE FUNCTION auto_complete_shifts_enhanced()
RETURNS INTEGER AS $$
DECLARE
    v_completed_count INTEGER := 0;
BEGIN
    -- Complete shifts that have passed their end time
    UPDATE driver_shifts 
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active'
      AND (
        -- Single date shifts
        (recurrence_pattern = 'single' AND shift_date = CURRENT_DATE)
        OR
        -- Date range shifts that are active today
        (recurrence_pattern != 'single' AND is_shift_active_on_date(id, CURRENT_DATE))
      )
      AND CURRENT_TIME > CASE 
          WHEN end_time < start_time 
          THEN end_time + interval '24 hours'
          ELSE end_time 
      END;
    
    GET DIAGNOSTICS v_completed_count = ROW_COUNT;
    RETURN v_completed_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- Intelligent Shift Classification Function
-- ============================================================================

-- Function to classify shift type based on time patterns
CREATE OR REPLACE FUNCTION classify_shift_by_time(
    p_start_time TIME,
    p_end_time TIME
) RETURNS shift_type AS $$
DECLARE
    v_start_minutes INTEGER;
    v_end_minutes INTEGER;
    v_tolerance INTEGER := 30; -- 30 minutes tolerance
BEGIN
    -- Convert times to minutes since midnight
    v_start_minutes := EXTRACT(HOUR FROM p_start_time) * 60 + EXTRACT(MINUTE FROM p_start_time);
    v_end_minutes := EXTRACT(HOUR FROM p_end_time) * 60 + EXTRACT(MINUTE FROM p_end_time);
    
    -- Check for standard day shift patterns (6AM-6PM, 7AM-7PM, 8AM-5PM, etc.)
    IF (
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 6AM-6PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1140) <= v_tolerance) OR -- 7AM-7PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 8AM-5PM
        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 6AM-5PM
        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 7AM-6PM
        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 960) <= v_tolerance) OR  -- 8AM-4PM
        (ABS(v_start_minutes - 540) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance)    -- 9AM-5PM
    ) THEN
        RETURN 'day';
    END IF;
    
    -- Check for standard night shift patterns (6PM-6AM, 7PM-7AM, etc.)
    -- Handle midnight crossing
    IF (
        (ABS(v_start_minutes - 1080) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 6PM-6AM
        (ABS(v_start_minutes - 1140) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 7PM-7AM
        (ABS(v_start_minutes - 1320) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 10PM-6AM
        (ABS(v_start_minutes - 1380) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 11PM-7AM
        (ABS(v_start_minutes - 1200) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 480) <= v_tolerance))    -- 8PM-8AM
    ) THEN
        RETURN 'night';
    END IF;
    
    -- If no standard pattern matches, return custom
    RETURN 'custom';
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically set display_type for new shifts
CREATE OR REPLACE FUNCTION set_display_type_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- If display_type is not explicitly set, compute it intelligently
    IF NEW.display_type IS NULL THEN
        -- If user selected day or night explicitly, respect that
        IF NEW.shift_type IN ('day', 'night') THEN
            NEW.display_type := NEW.shift_type;
        ELSE
            -- For custom shifts, use intelligent classification
            NEW.display_type := classify_shift_by_time(NEW.start_time, NEW.end_time);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic display_type setting
DROP TRIGGER IF EXISTS trigger_set_display_type ON driver_shifts;
CREATE TRIGGER trigger_set_display_type
    BEFORE INSERT OR UPDATE ON driver_shifts
    FOR EACH ROW
    EXECUTE FUNCTION set_display_type_trigger();

-- ============================================================================
-- Comments and Documentation
-- ============================================================================

COMMENT ON COLUMN driver_shifts.start_date IS 'Start date for date range shifts (NULL for single-date shifts)';
COMMENT ON COLUMN driver_shifts.end_date IS 'End date for date range shifts (NULL for single-date shifts)';
COMMENT ON COLUMN driver_shifts.recurrence_pattern IS 'Recurrence pattern: single, daily, weekly, weekdays, weekends, custom';
COMMENT ON COLUMN driver_shifts.display_type IS 'Computed display type for UI (may differ from shift_type for intelligent classification)';

COMMENT ON FUNCTION is_shift_active_on_date(INTEGER, DATE) IS 'Check if a shift is active on a specific date based on recurrence pattern';
COMMENT ON FUNCTION get_current_driver_for_truck_enhanced(INTEGER, DATE, TIME) IS 'Get current active driver for truck with date range support';
COMMENT ON FUNCTION get_active_shifts_for_date(DATE) IS 'Get all active shifts for a date with recurrence pattern support';
COMMENT ON FUNCTION auto_activate_shifts_enhanced() IS 'Enhanced auto-activation with date range and recurrence support';
COMMENT ON FUNCTION auto_complete_shifts_enhanced() IS 'Enhanced auto-completion with date range and recurrence support';
COMMENT ON FUNCTION classify_shift_by_time(TIME, TIME) IS 'Intelligently classify shift type based on time patterns';

-- ============================================================================
-- Migration Complete
-- ============================================================================

-- Log migration completion
INSERT INTO migration_log (migration_name, executed_at, description) 
VALUES (
    '023_enhance_shift_date_ranges', 
    CURRENT_TIMESTAMP, 
    'Enhanced shift scheduling with date ranges, recurrence patterns, and intelligent classification'
) ON CONFLICT (migration_name) DO UPDATE SET 
    executed_at = CURRENT_TIMESTAMP,
    description = EXCLUDED.description;
