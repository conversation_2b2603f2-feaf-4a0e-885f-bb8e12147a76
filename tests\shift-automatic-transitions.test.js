/**
 * Test Suite: Automatic Shift Status Transitions
 * Purpose: Validate automatic shift status transitions from scheduled → active → completed
 * Issues Addressed: ISSUE 1 - Missing Automatic Shift Status Transition
 * 
 * Test Scenarios:
 * 1. Basic automatic activation (scheduled → active)
 * 2. Basic automatic completion (active → completed)
 * 3. Edge case: Overlapping shifts
 * 4. Edge case: Missed activation times
 * 5. Edge case: Night shifts crossing midnight
 * 6. Integration with existing captureActiveDriverInfo() function
 */

const { Pool } = require('pg');
const assert = require('assert');

// Test database configuration
const testDbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'hauling_qr_test',
  user: 'postgres',
  password: 'PostgreSQLPassword'
};

class ShiftTransitionTester {
  constructor() {
    this.pool = new Pool(testDbConfig);
    this.testResults = [];
  }

  async setup() {
    console.log('🔧 Setting up test environment...');
    
    // Create test database if not exists
    await this.createTestDatabase();
    
    // Setup test data
    await this.setupTestData();
    
    console.log('✅ Test environment ready');
  }

  async createTestDatabase() {
    // Connect to default postgres database to create test database
    const adminPool = new Pool({
      ...testDbConfig,
      database: 'postgres'
    });

    try {
      await adminPool.query(`CREATE DATABASE hauling_qr_test`);
      console.log('📊 Test database created');
    } catch (error) {
      if (error.code !== '42P04') { // Database already exists
        throw error;
      }
      console.log('📊 Test database already exists');
    } finally {
      await adminPool.end();
    }
  }

  async setupTestData() {
    // Create necessary tables and enums for testing
    await this.pool.query(`
      -- Create enums if they don't exist
      DO $$ BEGIN
        CREATE TYPE shift_type AS ENUM ('day', 'night', 'custom');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;

      DO $$ BEGIN
        CREATE TYPE shift_status AS ENUM ('scheduled', 'active', 'completed', 'cancelled');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create test tables
    await this.pool.query(`
      CREATE TABLE IF NOT EXISTS dump_trucks (
        id SERIAL PRIMARY KEY,
        truck_number VARCHAR(20) UNIQUE NOT NULL,
        license_plate VARCHAR(20)
      );

      CREATE TABLE IF NOT EXISTS drivers (
        id SERIAL PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        employee_id VARCHAR(20) UNIQUE
      );

      CREATE TABLE IF NOT EXISTS driver_shifts (
        id SERIAL PRIMARY KEY,
        truck_id INTEGER NOT NULL REFERENCES dump_trucks(id),
        driver_id INTEGER NOT NULL REFERENCES drivers(id),
        shift_type shift_type NOT NULL DEFAULT 'day',
        shift_date DATE NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        status shift_status NOT NULL DEFAULT 'scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert test data
    await this.pool.query(`
      INSERT INTO dump_trucks (truck_number, license_plate) VALUES 
      ('T001', 'ABC123'),
      ('T002', 'DEF456')
      ON CONFLICT (truck_number) DO NOTHING;

      INSERT INTO drivers (full_name, employee_id) VALUES 
      ('John Doe', 'EMP001'),
      ('Jane Smith', 'EMP002'),
      ('Bob Wilson', 'EMP003')
      ON CONFLICT (employee_id) DO NOTHING;
    `);

    console.log('📋 Test data setup complete');
  }

  async testBasicActivation() {
    console.log('\n🧪 Testing: Basic Automatic Activation (scheduled → active)');
    
    // Create a shift that should be activated now
    const currentTime = new Date();
    const startTime = new Date(currentTime.getTime() - 5 * 60000); // 5 minutes ago
    const endTime = new Date(currentTime.getTime() + 55 * 60000); // 55 minutes from now

    const shiftResult = await this.pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES (1, 1, 'day', CURRENT_DATE, $1::time, $2::time, 'scheduled')
      RETURNING id
    `, [
      startTime.toTimeString().slice(0, 8),
      endTime.toTimeString().slice(0, 8)
    ]);

    const shiftId = shiftResult.rows[0].id;

    // Test the auto-activation function
    await this.pool.query(`
      UPDATE driver_shifts 
      SET status = 'active', updated_at = CURRENT_TIMESTAMP
      WHERE status = 'scheduled'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME >= start_time
        AND CURRENT_TIME < CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END
    `);

    // Verify activation
    const result = await this.pool.query(`
      SELECT status FROM driver_shifts WHERE id = $1
    `, [shiftId]);

    const success = result.rows[0].status === 'active';
    this.testResults.push({
      test: 'Basic Automatic Activation',
      success,
      details: success ? 'Shift correctly activated' : `Expected 'active', got '${result.rows[0].status}'`
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    return success;
  }

  async testBasicCompletion() {
    console.log('\n🧪 Testing: Basic Automatic Completion (active → completed)');
    
    // Create an active shift that should be completed
    const currentTime = new Date();
    const startTime = new Date(currentTime.getTime() - 65 * 60000); // 65 minutes ago
    const endTime = new Date(currentTime.getTime() - 5 * 60000); // 5 minutes ago

    const shiftResult = await this.pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES (1, 2, 'day', CURRENT_DATE, $1::time, $2::time, 'active')
      RETURNING id
    `, [
      startTime.toTimeString().slice(0, 8),
      endTime.toTimeString().slice(0, 8)
    ]);

    const shiftId = shiftResult.rows[0].id;

    // Test the auto-completion function
    await this.pool.query(`
      UPDATE driver_shifts 
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP
      WHERE status = 'active'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME > CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END
    `);

    // Verify completion
    const result = await this.pool.query(`
      SELECT status FROM driver_shifts WHERE id = $1
    `, [shiftId]);

    const success = result.rows[0].status === 'completed';
    this.testResults.push({
      test: 'Basic Automatic Completion',
      success,
      details: success ? 'Shift correctly completed' : `Expected 'completed', got '${result.rows[0].status}'`
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    return success;
  }

  async testOverlappingShifts() {
    console.log('\n🧪 Testing: Overlapping Shifts Handling');
    
    const currentTime = new Date();
    
    // Create overlapping shifts
    const shift1Start = new Date(currentTime.getTime() - 30 * 60000); // 30 minutes ago
    const shift1End = new Date(currentTime.getTime() + 30 * 60000); // 30 minutes from now
    const shift2Start = new Date(currentTime.getTime() - 10 * 60000); // 10 minutes ago
    const shift2End = new Date(currentTime.getTime() + 50 * 60000); // 50 minutes from now

    // Insert overlapping shifts
    await this.pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES 
      (2, 1, 'day', CURRENT_DATE, $1::time, $2::time, 'scheduled'),
      (2, 2, 'day', CURRENT_DATE, $3::time, $4::time, 'scheduled')
    `, [
      shift1Start.toTimeString().slice(0, 8),
      shift1End.toTimeString().slice(0, 8),
      shift2Start.toTimeString().slice(0, 8),
      shift2End.toTimeString().slice(0, 8)
    ]);

    // Test activation with overlap handling
    await this.pool.query(`
      UPDATE driver_shifts 
      SET status = 'active', updated_at = CURRENT_TIMESTAMP
      WHERE status = 'scheduled'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME >= start_time
        AND CURRENT_TIME < CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END
    `);

    // Check how many shifts are active for truck 2
    const activeShifts = await this.pool.query(`
      SELECT COUNT(*) as count FROM driver_shifts 
      WHERE truck_id = 2 AND status = 'active' AND shift_date = CURRENT_DATE
    `);

    const success = activeShifts.rows[0].count >= 1; // At least one should be active
    this.testResults.push({
      test: 'Overlapping Shifts Handling',
      success,
      details: success ? `${activeShifts.rows[0].count} shifts activated` : 'No shifts activated'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    return success;
  }

  async testNightShiftMidnightCrossing() {
    console.log('\n🧪 Testing: Night Shift Midnight Crossing');
    
    // Create a night shift that crosses midnight (18:00 to 06:00)
    await this.pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES (1, 3, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled')
    `);

    // Test midnight crossing logic
    const nightShiftQuery = `
      SELECT id, status FROM driver_shifts 
      WHERE shift_type = 'night' 
        AND start_time > end_time 
        AND shift_date = CURRENT_DATE
        AND (
          (CURRENT_TIME >= start_time) OR 
          (CURRENT_TIME <= end_time)
        )
    `;

    const result = await this.pool.query(nightShiftQuery);
    const success = result.rows.length > 0;
    
    this.testResults.push({
      test: 'Night Shift Midnight Crossing',
      success,
      details: success ? 'Night shift midnight logic working' : 'Night shift midnight logic failed'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    return success;
  }

  async testCaptureActiveDriverIntegration() {
    console.log('\n🧪 Testing: Integration with captureActiveDriverInfo()');
    
    // Create an active shift
    const currentTime = new Date();
    const startTime = new Date(currentTime.getTime() - 10 * 60000);
    const endTime = new Date(currentTime.getTime() + 50 * 60000);

    await this.pool.query(`
      INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
      VALUES (1, 1, 'day', CURRENT_DATE, $1::time, $2::time, 'active')
    `, [
      startTime.toTimeString().slice(0, 8),
      endTime.toTimeString().slice(0, 8)
    ]);

    // Test the get_current_driver_for_truck equivalent query
    const driverQuery = `
      SELECT 
        ds.driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.truck_id = $1
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
            END
      ORDER BY ds.created_at DESC
      LIMIT 1
    `;

    const result = await this.pool.query(driverQuery, [1]);
    const success = result.rows.length > 0 && result.rows[0].driver_name === 'John Doe';
    
    this.testResults.push({
      test: 'captureActiveDriverInfo Integration',
      success,
      details: success ? 'Active driver correctly retrieved' : 'Failed to retrieve active driver'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    return success;
  }

  async runAllTests() {
    console.log('🚀 Starting Automatic Shift Transition Tests\n');
    
    try {
      await this.setup();
      
      const tests = [
        () => this.testBasicActivation(),
        () => this.testBasicCompletion(),
        () => this.testOverlappingShifts(),
        () => this.testNightShiftMidnightCrossing(),
        () => this.testCaptureActiveDriverIntegration()
      ];

      let passCount = 0;
      for (const test of tests) {
        if (await test()) {
          passCount++;
        }
      }

      console.log('\n📊 Test Results Summary:');
      console.log('========================');
      this.testResults.forEach(result => {
        console.log(`${result.success ? '✅' : '❌'} ${result.test}: ${result.details}`);
      });
      
      console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);
      
      if (passCount === tests.length) {
        console.log('🎉 All automatic shift transition tests PASSED!');
        console.log('✅ Ready to implement automatic shift transition system');
      } else {
        console.log('⚠️  Some tests failed - review implementation before proceeding');
      }

    } catch (error) {
      console.error('❌ Test execution failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');
    await this.pool.end();
    console.log('✅ Cleanup complete');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new ShiftTransitionTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ShiftTransitionTester;
