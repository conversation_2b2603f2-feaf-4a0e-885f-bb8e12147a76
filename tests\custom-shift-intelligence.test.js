/**
 * Test Suite: Custom Shift Intelligence
 * Purpose: Test intelligent custom shift time classification logic
 * Issues Addressed: ADDITIONAL ENHANCEMENT - Custom Shift Time Classification
 * 
 * Problem: When creating a custom shift (e.g., 8 AM - 5 PM), the system needs to 
 * intelligently determine if it should be classified as 'day' or 'custom' for display purposes.
 * 
 * Required Logic:
 * 1. If custom shift times match standard patterns:
 *    - 6 AM - 6 PM or similar → classify as 'day' for display
 *    - 6 PM - 6 AM or similar → classify as 'night' for display
 * 2. If custom shift times don't match standard patterns → classify as 'custom' for display
 * 3. Store both shift_type and computed display_type in database
 * 4. Use display_type for UI labels in getDriverDisplay() function
 * 
 * Test Scenarios:
 * 1. Standard day shift patterns (6AM-6PM, 7AM-7PM, 8AM-5PM)
 * 2. Standard night shift patterns (6PM-6AM, 10PM-6AM, 11PM-7AM)
 * 3. Non-standard custom patterns (9AM-2PM, 2PM-10PM, split shifts)
 * 4. Edge cases and boundary conditions
 * 5. Database storage of both shift_type and display_type
 * 6. UI integration with intelligent classification
 */

const assert = require('assert');

class CustomShiftIntelligenceTester {
  constructor() {
    this.testResults = [];
  }

  // Core intelligence function for shift classification
  classifyShiftByTime(startTime, endTime) {
    // Parse time strings (HH:MM or HH:MM:SS format)
    const parseTime = (timeStr) => {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + (minutes || 0); // Convert to minutes since midnight
    };

    const startMinutes = parseTime(startTime);
    const endMinutes = parseTime(endTime);

    // Define standard shift patterns (in minutes since midnight)
    const dayShiftPatterns = [
      { start: 6 * 60, end: 18 * 60, name: '6AM-6PM' },      // 360-1080
      { start: 7 * 60, end: 19 * 60, name: '7AM-7PM' },      // 420-1140
      { start: 8 * 60, end: 17 * 60, name: '8AM-5PM' },      // 480-1020
      { start: 6 * 60, end: 17 * 60, name: '6AM-5PM' },      // 360-1020
      { start: 7 * 60, end: 18 * 60, name: '7AM-6PM' },      // 420-1080
      { start: 8 * 60, end: 16 * 60, name: '8AM-4PM' },      // 480-960
      { start: 9 * 60, end: 17 * 60, name: '9AM-5PM' }       // 540-1020
    ];

    const nightShiftPatterns = [
      { start: 18 * 60, end: 6 * 60, name: '6PM-6AM' },      // 1080-360 (crosses midnight)
      { start: 19 * 60, end: 7 * 60, name: '7PM-7AM' },      // 1140-420 (crosses midnight)
      { start: 22 * 60, end: 6 * 60, name: '10PM-6AM' },     // 1320-360 (crosses midnight)
      { start: 23 * 60, end: 7 * 60, name: '11PM-7AM' },     // 1380-420 (crosses midnight)
      { start: 20 * 60, end: 8 * 60, name: '8PM-8AM' }       // 1200-480 (crosses midnight)
    ];

    // Tolerance for matching patterns (±30 minutes)
    const tolerance = 30;

    // Check day shift patterns
    for (const pattern of dayShiftPatterns) {
      if (Math.abs(startMinutes - pattern.start) <= tolerance &&
          Math.abs(endMinutes - pattern.end) <= tolerance) {
        return {
          classification: 'day',
          confidence: 'high',
          matchedPattern: pattern.name,
          reason: `Matches standard day shift pattern ${pattern.name}`
        };
      }
    }

    // Check night shift patterns (handle midnight crossing)
    for (const pattern of nightShiftPatterns) {
      const nightShiftMatch = (
        Math.abs(startMinutes - pattern.start) <= tolerance &&
        (
          // End time is next day (smaller number due to midnight crossing)
          (endMinutes < startMinutes && Math.abs(endMinutes - pattern.end) <= tolerance) ||
          // End time is same day but very late (close to midnight)
          (endMinutes > startMinutes && Math.abs(endMinutes - (pattern.end + 24 * 60)) <= tolerance)
        )
      );

      if (nightShiftMatch) {
        return {
          classification: 'night',
          confidence: 'high',
          matchedPattern: pattern.name,
          reason: `Matches standard night shift pattern ${pattern.name}`
        };
      }
    }

    // If no standard pattern matches, classify as custom
    return {
      classification: 'custom',
      confidence: 'definitive',
      matchedPattern: null,
      reason: 'Does not match any standard day or night shift patterns'
    };
  }

  // Enhanced classification with additional business logic
  classifyShiftWithBusinessRules(startTime, endTime, shiftType = 'custom') {
    const baseClassification = this.classifyShiftByTime(startTime, endTime);

    // If user explicitly selected day or night, respect that choice
    if (shiftType === 'day' || shiftType === 'night') {
      return {
        ...baseClassification,
        classification: shiftType,
        userOverride: true,
        reason: `User explicitly selected ${shiftType} shift type`
      };
    }

    // For custom shifts, use intelligent classification
    return {
      ...baseClassification,
      userOverride: false,
      displayType: baseClassification.classification,
      shiftType: 'custom' // Always store as custom in database
    };
  }

  async testStandardDayShiftPatterns() {
    console.log('\n🧪 Testing: Standard Day Shift Patterns');

    const dayShiftTests = [
      { start: '06:00', end: '18:00', expected: 'day', pattern: '6AM-6PM' },
      { start: '07:00', end: '19:00', expected: 'day', pattern: '7AM-7PM' },
      { start: '08:00', end: '17:00', expected: 'day', pattern: '8AM-5PM' },
      { start: '06:30', end: '18:30', expected: 'day', pattern: '6AM-6PM (with tolerance)' },
      { start: '08:15', end: '16:45', expected: 'day', pattern: '8AM-4PM (with tolerance)' },
      { start: '09:00', end: '17:00', expected: 'day', pattern: '9AM-5PM' }
    ];

    let allPassed = true;

    for (const test of dayShiftTests) {
      const result = this.classifyShiftByTime(test.start, test.end);
      const passed = result.classification === test.expected;
      
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.start}-${test.end}: ${result.classification} (${result.reason})`);
    }

    this.testResults.push({
      test: 'Standard Day Shift Patterns',
      success: allPassed,
      details: allPassed ? 'All day shift patterns classified correctly' : 'Some day shift patterns failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async testStandardNightShiftPatterns() {
    console.log('\n🧪 Testing: Standard Night Shift Patterns');

    const nightShiftTests = [
      { start: '18:00', end: '06:00', expected: 'night', pattern: '6PM-6AM' },
      { start: '19:00', end: '07:00', expected: 'night', pattern: '7PM-7AM' },
      { start: '22:00', end: '06:00', expected: 'night', pattern: '10PM-6AM' },
      { start: '23:00', end: '07:00', expected: 'night', pattern: '11PM-7AM' },
      { start: '18:30', end: '06:30', expected: 'night', pattern: '6PM-6AM (with tolerance)' },
      { start: '20:00', end: '08:00', expected: 'night', pattern: '8PM-8AM' }
    ];

    let allPassed = true;

    for (const test of nightShiftTests) {
      const result = this.classifyShiftByTime(test.start, test.end);
      const passed = result.classification === test.expected;
      
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.start}-${test.end}: ${result.classification} (${result.reason})`);
    }

    this.testResults.push({
      test: 'Standard Night Shift Patterns',
      success: allPassed,
      details: allPassed ? 'All night shift patterns classified correctly' : 'Some night shift patterns failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async testNonStandardCustomPatterns() {
    console.log('\n🧪 Testing: Non-Standard Custom Patterns');

    const customShiftTests = [
      { start: '09:00', end: '14:00', expected: 'custom', pattern: 'Short morning shift' },
      { start: '14:00', end: '22:00', expected: 'custom', pattern: 'Afternoon to evening' },
      { start: '10:00', end: '15:00', expected: 'custom', pattern: 'Mid-day shift' },
      { start: '12:00', end: '20:00', expected: 'custom', pattern: 'Noon to evening' },
      { start: '04:00', end: '12:00', expected: 'custom', pattern: 'Early morning to noon' },
      { start: '16:00', end: '24:00', expected: 'custom', pattern: 'Evening to midnight' }
    ];

    let allPassed = true;

    for (const test of customShiftTests) {
      const result = this.classifyShiftByTime(test.start, test.end);
      const passed = result.classification === test.expected;
      
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.start}-${test.end}: ${result.classification} (${result.reason})`);
    }

    this.testResults.push({
      test: 'Non-Standard Custom Patterns',
      success: allPassed,
      details: allPassed ? 'All custom patterns classified correctly' : 'Some custom patterns failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async testEdgeCasesAndBoundaryConditions() {
    console.log('\n🧪 Testing: Edge Cases and Boundary Conditions');

    const edgeCaseTests = [
      { start: '05:30', end: '18:30', expected: 'day', description: 'Just outside day pattern tolerance' },
      { start: '06:31', end: '18:31', expected: 'day', description: 'Just inside day pattern tolerance' },
      { start: '17:30', end: '05:30', expected: 'night', description: 'Just outside night pattern tolerance' },
      { start: '18:31', end: '06:31', expected: 'night', description: 'Just inside night pattern tolerance' },
      { start: '00:00', end: '12:00', expected: 'custom', description: 'Midnight to noon' },
      { start: '12:00', end: '00:00', expected: 'custom', description: 'Noon to midnight' },
      { start: '23:59', end: '00:01', expected: 'custom', description: 'Very short midnight crossing' }
    ];

    let allPassed = true;

    for (const test of edgeCaseTests) {
      const result = this.classifyShiftByTime(test.start, test.end);
      const passed = result.classification === test.expected;
      
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.description}: ${result.classification}`);
    }

    this.testResults.push({
      test: 'Edge Cases and Boundary Conditions',
      success: allPassed,
      details: allPassed ? 'All edge cases handled correctly' : 'Some edge cases failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async testBusinessRulesIntegration() {
    console.log('\n🧪 Testing: Business Rules Integration');

    const businessRuleTests = [
      {
        start: '08:00', end: '17:00', userType: 'custom',
        expectedClassification: 'day', expectedDisplayType: 'day',
        description: 'Custom shift matching day pattern'
      },
      {
        start: '18:00', end: '06:00', userType: 'custom',
        expectedClassification: 'night', expectedDisplayType: 'night',
        description: 'Custom shift matching night pattern'
      },
      {
        start: '10:00', end: '15:00', userType: 'custom',
        expectedClassification: 'custom', expectedDisplayType: 'custom',
        description: 'Custom shift with unique pattern'
      },
      {
        start: '10:00', end: '15:00', userType: 'day',
        expectedClassification: 'day', expectedDisplayType: 'day',
        description: 'User override: force day classification'
      },
      {
        start: '08:00', end: '17:00', userType: 'night',
        expectedClassification: 'night', expectedDisplayType: 'night',
        description: 'User override: force night classification'
      }
    ];

    let allPassed = true;

    for (const test of businessRuleTests) {
      const result = this.classifyShiftWithBusinessRules(test.start, test.end, test.userType);
      const passed = result.classification === test.expectedClassification;
      
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.description}`);
      console.log(`      Result: ${result.classification} (override: ${result.userOverride})`);
    }

    this.testResults.push({
      test: 'Business Rules Integration',
      success: allPassed,
      details: allPassed ? 'All business rules applied correctly' : 'Some business rules failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async testDatabaseStorageSimulation() {
    console.log('\n🧪 Testing: Database Storage Simulation');

    // Simulate database storage with both shift_type and display_type
    const mockShiftCreation = (startTime, endTime, userSelectedType = 'custom') => {
      const classification = this.classifyShiftWithBusinessRules(startTime, endTime, userSelectedType);
      
      return {
        shift_type: userSelectedType, // What user selected
        display_type: classification.classification, // What system computed
        start_time: startTime,
        end_time: endTime,
        classification_confidence: classification.confidence,
        matched_pattern: classification.matchedPattern,
        user_override: classification.userOverride
      };
    };

    const testShifts = [
      { start: '08:00', end: '17:00', userType: 'custom' }, // Should display as day
      { start: '18:00', end: '06:00', userType: 'custom' }, // Should display as night
      { start: '10:00', end: '14:00', userType: 'custom' }, // Should display as custom
      { start: '10:00', end: '14:00', userType: 'day' }     // Should display as day (override)
    ];

    let allPassed = true;
    const createdShifts = [];

    for (const test of testShifts) {
      const shift = mockShiftCreation(test.start, test.end, test.userType);
      createdShifts.push(shift);

      const expectedDisplay = test.userType !== 'custom' ? test.userType : 
                             (test.start === '08:00' ? 'day' : 
                              test.start === '18:00' ? 'night' : 'custom');

      const passed = shift.display_type === expectedDisplay;
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${test.start}-${test.end} (${test.userType}): display as ${shift.display_type}`);
    }

    this.testResults.push({
      test: 'Database Storage Simulation',
      success: allPassed,
      details: allPassed ? 'Database storage logic works correctly' : 'Database storage logic failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    console.log(`   📊 Created ${createdShifts.length} shifts with intelligent classification`);
    
    return allPassed;
  }

  async testUIIntegrationSimulation() {
    console.log('\n🧪 Testing: UI Integration Simulation');

    // Simulate getDriverDisplay function using display_type
    const mockGetDriverDisplay = (trip) => {
      const shiftTypeDisplayMap = {
        'day': '☀️ Day Shift',
        'night': '🌙 Night Shift',
        'custom': '🔧 Custom Shift'
      };

      // Use display_type instead of shift_type for UI
      const displayType = trip.display_type || trip.shift_type;
      const displayText = shiftTypeDisplayMap[displayType] || '❓ Unknown Shift';

      return {
        driver_name: trip.driver_name,
        shift_display: displayText,
        shift_type: trip.shift_type, // Original type
        display_type: displayType,   // Computed type
        intelligent: trip.shift_type === 'custom' && displayType !== 'custom'
      };
    };

    const testTrips = [
      {
        driver_name: 'John Doe',
        shift_type: 'custom',
        display_type: 'day', // Intelligent classification
        start_time: '08:00', end_time: '17:00'
      },
      {
        driver_name: 'Jane Smith',
        shift_type: 'custom',
        display_type: 'night', // Intelligent classification
        start_time: '18:00', end_time: '06:00'
      },
      {
        driver_name: 'Bob Wilson',
        shift_type: 'custom',
        display_type: 'custom', // Truly custom
        start_time: '10:00', end_time: '14:00'
      },
      {
        driver_name: 'Alice Johnson',
        shift_type: 'day',
        display_type: 'day', // Standard day shift
        start_time: '06:00', end_time: '18:00'
      }
    ];

    let allPassed = true;

    for (const trip of testTrips) {
      const display = mockGetDriverDisplay(trip);
      
      const expectedDisplay = trip.display_type === 'day' ? '☀️ Day Shift' :
                             trip.display_type === 'night' ? '🌙 Night Shift' :
                             '🔧 Custom Shift';

      const passed = display.shift_display === expectedDisplay;
      if (!passed) allPassed = false;

      console.log(`   ${passed ? '✅' : '❌'} ${trip.driver_name}: ${display.shift_display} ${display.intelligent ? '(intelligent)' : ''}`);
    }

    this.testResults.push({
      test: 'UI Integration Simulation',
      success: allPassed,
      details: allPassed ? 'UI correctly uses intelligent classification' : 'UI integration failed'
    });

    console.log(allPassed ? '✅ PASS' : '❌ FAIL');
    return allPassed;
  }

  async runAllTests() {
    console.log('🚀 Starting Custom Shift Intelligence Tests\n');
    
    try {
      const tests = [
        () => this.testStandardDayShiftPatterns(),
        () => this.testStandardNightShiftPatterns(),
        () => this.testNonStandardCustomPatterns(),
        () => this.testEdgeCasesAndBoundaryConditions(),
        () => this.testBusinessRulesIntegration(),
        () => this.testDatabaseStorageSimulation(),
        () => this.testUIIntegrationSimulation()
      ];

      let passCount = 0;
      for (const test of tests) {
        if (await test()) {
          passCount++;
        }
      }

      console.log('\n📊 Test Results Summary:');
      console.log('========================');
      this.testResults.forEach(result => {
        console.log(`${result.success ? '✅' : '❌'} ${result.test}: ${result.details}`);
      });
      
      console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);
      
      if (passCount === tests.length) {
        console.log('🎉 All custom shift intelligence tests PASSED!');
        console.log('✅ Ready to implement intelligent shift classification');
        console.log('🧠 Key features validated:');
        console.log('   1. Time-based pattern recognition');
        console.log('   2. Standard day/night shift detection');
        console.log('   3. Custom pattern identification');
        console.log('   4. User override support');
        console.log('   5. Database storage with dual types');
        console.log('   6. UI integration with intelligent display');
      } else {
        console.log('⚠️  Some tests failed - review implementation before proceeding');
      }

    } catch (error) {
      console.error('❌ Test execution failed:', error);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new CustomShiftIntelligenceTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CustomShiftIntelligenceTester;
