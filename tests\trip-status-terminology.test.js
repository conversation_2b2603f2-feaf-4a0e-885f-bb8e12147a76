/**
 * Test Suite: Trip Status Terminology Update
 * Purpose: Validate trip_status enum update from 'breakdown' to 'stopped'
 * Issues Addressed: ISSUE 5 - Trip Status Terminology Update
 * 
 * Current Status: trip_status enum includes 'breakdown' for stopped trips
 * Required Change: Replace 'breakdown' with 'stopped' (more general term)
 * 
 * Components to Update:
 * 1. Database schema (trip_status enum)
 * 2. Scanner logic (server/routes/scanner.js)
 * 3. Frontend components (TripsTable.js, status displays)
 * 4. Business logic (business-trip-monitor.js)
 * 5. API validation (server/routes/trips.js)
 * 
 * Test Scenarios:
 * 1. Database enum update validation
 * 2. Scanner logic compatibility
 * 3. Frontend status display updates
 * 4. Business logic compatibility
 * 5. API validation schema updates
 * 6. Backward compatibility during migration
 */

const { Pool } = require('pg');
const assert = require('assert');

// Test database configuration
const testDbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'hauling_qr_test',
  user: 'postgres',
  password: 'PostgreSQLPassword'
};

class TripStatusTerminologyTester {
  constructor() {
    this.pool = new Pool(testDbConfig);
    this.testResults = [];
  }

  async setup() {
    console.log('🔧 Setting up trip status terminology test environment...');
    
    // Create test database if not exists
    await this.createTestDatabase();
    
    // Setup test schema with current and updated enums
    await this.setupTestSchema();
    
    console.log('✅ Test environment ready');
  }

  async createTestDatabase() {
    const adminPool = new Pool({
      ...testDbConfig,
      database: 'postgres'
    });

    try {
      await adminPool.query(`CREATE DATABASE hauling_qr_test`);
      console.log('📊 Test database created');
    } catch (error) {
      if (error.code !== '42P04') {
        throw error;
      }
      console.log('📊 Test database already exists');
    } finally {
      await adminPool.end();
    }
  }

  async setupTestSchema() {
    // Create current trip_status enum (with breakdown)
    await this.pool.query(`
      DROP TYPE IF EXISTS trip_status CASCADE;
      
      CREATE TYPE trip_status AS ENUM (
        'assigned',
        'loading_start',
        'loading_end', 
        'unloading_start',
        'unloading_end',
        'trip_completed',
        'exception_pending',
        'cancelled',
        'breakdown'
      );
    `);

    // Create test tables
    await this.pool.query(`
      CREATE TABLE IF NOT EXISTS assignments (
        id SERIAL PRIMARY KEY,
        assignment_code VARCHAR(50) UNIQUE,
        status VARCHAR(20) DEFAULT 'assigned'
      );

      CREATE TABLE IF NOT EXISTS trip_logs (
        id SERIAL PRIMARY KEY,
        assignment_id INTEGER NOT NULL REFERENCES assignments(id),
        trip_number INTEGER NOT NULL,
        status trip_status NOT NULL DEFAULT 'assigned',
        loading_start_time TIMESTAMP,
        loading_end_time TIMESTAMP,
        unloading_start_time TIMESTAMP,
        unloading_end_time TIMESTAMP,
        trip_completed_time TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert test data with breakdown status
    await this.pool.query(`
      INSERT INTO assignments (assignment_code, status) VALUES 
      ('ASG001', 'assigned'),
      ('ASG002', 'in_progress');

      INSERT INTO trip_logs (assignment_id, trip_number, status) VALUES 
      (1, 1, 'loading_start'),
      (1, 2, 'breakdown'),
      (2, 1, 'trip_completed'),
      (2, 2, 'breakdown');
    `);

    console.log('📋 Test schema and data setup complete');
  }

  async testDatabaseEnumUpdate() {
    console.log('\n🧪 Testing: Database Enum Update (breakdown → stopped)');

    try {
      // Step 1: Verify current breakdown status exists
      const currentBreakdownTrips = await this.pool.query(`
        SELECT COUNT(*) as count FROM trip_logs WHERE status = 'breakdown'
      `);

      console.log(`   📊 Found ${currentBreakdownTrips.rows[0].count} trips with 'breakdown' status`);

      // Step 2: Create updated enum with 'stopped' instead of 'breakdown'
      await this.pool.query(`
        CREATE TYPE trip_status_new AS ENUM (
          'assigned',
          'loading_start',
          'loading_end', 
          'unloading_start',
          'unloading_end',
          'trip_completed',
          'exception_pending',
          'cancelled',
          'stopped'
        );
      `);

      // Step 3: Update existing breakdown trips to stopped
      await this.pool.query(`
        UPDATE trip_logs SET status = 'stopped'::text::trip_status_new 
        WHERE status = 'breakdown'
      `);

      // Step 4: Alter table to use new enum
      await this.pool.query(`
        ALTER TABLE trip_logs 
        ALTER COLUMN status TYPE trip_status_new 
        USING status::text::trip_status_new
      `);

      // Step 5: Replace old enum
      await this.pool.query(`
        DROP TYPE trip_status;
        ALTER TYPE trip_status_new RENAME TO trip_status;
      `);

      // Step 6: Verify migration
      const stoppedTrips = await this.pool.query(`
        SELECT COUNT(*) as count FROM trip_logs WHERE status = 'stopped'
      `);

      const success = stoppedTrips.rows[0].count === currentBreakdownTrips.rows[0].count;

      this.testResults.push({
        test: 'Database Enum Update',
        success,
        details: success ? 
          `Successfully migrated ${stoppedTrips.rows[0].count} trips from 'breakdown' to 'stopped'` : 
          'Enum migration failed'
      });

      console.log(success ? '✅ PASS' : '❌ FAIL');
      return success;
    } catch (error) {
      this.testResults.push({
        test: 'Database Enum Update',
        success: false,
        details: `Migration error: ${error.message}`
      });

      console.log('❌ FAIL');
      return false;
    }
  }

  async testScannerLogicCompatibility() {
    console.log('\n🧪 Testing: Scanner Logic Compatibility');

    // Mock scanner logic that needs to be updated
    const mockScannerLogic = {
      // Current logic (uses 'breakdown')
      handleBreakdownStatus: (tripStatus) => {
        return tripStatus === 'breakdown' ? 'Trip is broken down' : 'Trip is active';
      },

      // Updated logic (uses 'stopped')
      handleStoppedStatus: (tripStatus) => {
        return tripStatus === 'stopped' ? 'Trip is stopped' : 'Trip is active';
      },

      // Logic for creating new trips after breakdown/stopped
      canCreateNewTrip: (tripStatus) => {
        return ['trip_completed', 'stopped', 'cancelled'].includes(tripStatus);
      }
    };

    // Test current logic
    const currentResult = mockScannerLogic.handleBreakdownStatus('breakdown');
    const currentCanCreate = mockScannerLogic.canCreateNewTrip('breakdown');

    // Test updated logic
    const updatedResult = mockScannerLogic.handleStoppedStatus('stopped');
    const updatedCanCreate = mockScannerLogic.canCreateNewTrip('stopped');

    const success = currentResult.includes('broken down') && 
                   updatedResult.includes('stopped') &&
                   currentCanCreate === false && // breakdown not in original list
                   updatedCanCreate === true; // stopped should allow new trips

    this.testResults.push({
      test: 'Scanner Logic Compatibility',
      success,
      details: success ? 
        'Scanner logic correctly handles both breakdown and stopped statuses' : 
        'Scanner logic compatibility failed'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    console.log(`   🔍 Current: "${currentResult}" (can create new: ${currentCanCreate})`);
    console.log(`   🔍 Updated: "${updatedResult}" (can create new: ${updatedCanCreate})`);
    
    return success;
  }

  async testFrontendStatusDisplayUpdates() {
    console.log('\n🧪 Testing: Frontend Status Display Updates');

    // Mock frontend status display logic
    const mockStatusDisplay = {
      // Current status descriptions (includes breakdown)
      currentStatusDescriptions: {
        'assigned': 'Trip assigned to driver',
        'loading_start': 'Loading in progress',
        'loading_end': 'Loading finished, traveling',
        'unloading_start': 'Unloading in progress',
        'unloading_end': 'Unloading finished, returning',
        'trip_completed': 'Fully completed trip',
        'cancelled': 'Cancelled or rejected',
        'breakdown': 'Trip paused due to breakdown'
      },

      // Updated status descriptions (uses stopped)
      updatedStatusDescriptions: {
        'assigned': 'Trip assigned to driver',
        'loading_start': 'Loading in progress',
        'loading_end': 'Loading finished, traveling',
        'unloading_start': 'Unloading in progress',
        'unloading_end': 'Unloading finished, returning',
        'trip_completed': 'Fully completed trip',
        'cancelled': 'Cancelled or rejected',
        'stopped': 'Trip stopped (breakdown, repair, or finished)'
      },

      // Status icons and colors
      getStatusDisplay: (status, useUpdated = false) => {
        const descriptions = useUpdated ? 
          this.updatedStatusDescriptions : 
          this.currentStatusDescriptions;

        const statusConfig = {
          'breakdown': { icon: '🔧', color: 'red', label: 'Breakdown' },
          'stopped': { icon: '⏹️', color: 'orange', label: 'Stopped' }
        };

        return {
          description: descriptions[status] || 'Unknown status',
          ...statusConfig[status] || { icon: '❓', color: 'gray', label: 'Unknown' }
        };
      }
    };

    // Test current and updated displays
    const currentDisplay = mockStatusDisplay.getStatusDisplay('breakdown', false);
    const updatedDisplay = mockStatusDisplay.getStatusDisplay('stopped', true);

    const success = currentDisplay.description.includes('breakdown') &&
                   updatedDisplay.description.includes('stopped') &&
                   currentDisplay.icon === '🔧' &&
                   updatedDisplay.icon === '⏹️';

    this.testResults.push({
      test: 'Frontend Status Display Updates',
      success,
      details: success ? 
        'Frontend correctly displays both breakdown and stopped statuses' : 
        'Frontend status display update failed'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    console.log(`   🎨 Current: ${currentDisplay.icon} ${currentDisplay.label} - ${currentDisplay.description}`);
    console.log(`   🎨 Updated: ${updatedDisplay.icon} ${updatedDisplay.label} - ${updatedDisplay.description}`);
    
    return success;
  }

  async testBusinessLogicCompatibility() {
    console.log('\n🧪 Testing: Business Logic Compatibility');

    // Mock business logic that handles trip statuses
    const mockBusinessLogic = {
      // Trip completion logic
      isCompletedTrip: (status) => {
        return ['trip_completed', 'stopped', 'cancelled'].includes(status);
      },

      // Operational status mapping
      getOperationalStatus: (tripStatus) => {
        const statusMap = {
          'assigned': 'IDLE',
          'loading_start': 'ACTIVE',
          'loading_end': 'ACTIVE',
          'unloading_start': 'ACTIVE',
          'unloading_end': 'ACTIVE',
          'trip_completed': 'COMPLETED',
          'breakdown': 'MAINTENANCE', // Old
          'stopped': 'MAINTENANCE',   // New
          'cancelled': 'CANCELLED'
        };

        return statusMap[tripStatus] || 'UNKNOWN';
      },

      // Analytics and reporting
      getTripMetrics: (trips) => {
        const metrics = {
          completed: 0,
          active: 0,
          stopped: 0,
          breakdown: 0
        };

        trips.forEach(trip => {
          if (trip.status === 'trip_completed') metrics.completed++;
          else if (['loading_start', 'loading_end', 'unloading_start', 'unloading_end'].includes(trip.status)) metrics.active++;
          else if (trip.status === 'stopped') metrics.stopped++;
          else if (trip.status === 'breakdown') metrics.breakdown++;
        });

        return metrics;
      }
    };

    // Test with sample trips
    const sampleTrips = [
      { id: 1, status: 'trip_completed' },
      { id: 2, status: 'breakdown' },
      { id: 3, status: 'stopped' },
      { id: 4, status: 'loading_start' }
    ];

    const breakdownCompleted = mockBusinessLogic.isCompletedTrip('breakdown');
    const stoppedCompleted = mockBusinessLogic.isCompletedTrip('stopped');
    const breakdownOperational = mockBusinessLogic.getOperationalStatus('breakdown');
    const stoppedOperational = mockBusinessLogic.getOperationalStatus('stopped');
    const metrics = mockBusinessLogic.getTripMetrics(sampleTrips);

    const success = !breakdownCompleted && // breakdown should not be considered completed
                   stoppedCompleted && // stopped should be considered completed
                   breakdownOperational === 'MAINTENANCE' &&
                   stoppedOperational === 'MAINTENANCE' &&
                   metrics.breakdown === 1 &&
                   metrics.stopped === 1;

    this.testResults.push({
      test: 'Business Logic Compatibility',
      success,
      details: success ? 
        'Business logic correctly handles both breakdown and stopped statuses' : 
        'Business logic compatibility failed'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    console.log(`   📊 Metrics: ${JSON.stringify(metrics)}`);
    console.log(`   🔍 Breakdown completed: ${breakdownCompleted}, operational: ${breakdownOperational}`);
    console.log(`   🔍 Stopped completed: ${stoppedCompleted}, operational: ${stoppedOperational}`);
    
    return success;
  }

  async testAPIValidationSchemaUpdates() {
    console.log('\n🧪 Testing: API Validation Schema Updates');

    // Mock API validation schemas
    const mockValidation = {
      // Current schema (includes breakdown)
      currentTripSchema: {
        status: {
          type: 'string',
          enum: [
            'assigned', 'loading_start', 'loading_end',
            'unloading_start', 'unloading_end', 'trip_completed',
            'auto_assignment', 'dynamic_route', 'cancelled', 'breakdown'
          ]
        }
      },

      // Updated schema (uses stopped)
      updatedTripSchema: {
        status: {
          type: 'string',
          enum: [
            'assigned', 'loading_start', 'loading_end',
            'unloading_start', 'unloading_end', 'trip_completed',
            'auto_assignment', 'dynamic_route', 'cancelled', 'stopped'
          ]
        }
      },

      // Validation function
      validateStatus: (status, useUpdated = false) => {
        const schema = useUpdated ? this.updatedTripSchema : this.currentTripSchema;
        return schema.status.enum.includes(status);
      }
    };

    // Test validation
    const breakdownValidCurrent = mockValidation.validateStatus('breakdown', false);
    const breakdownValidUpdated = mockValidation.validateStatus('breakdown', true);
    const stoppedValidCurrent = mockValidation.validateStatus('stopped', false);
    const stoppedValidUpdated = mockValidation.validateStatus('stopped', true);

    const success = breakdownValidCurrent && // breakdown valid in current
                   !breakdownValidUpdated && // breakdown invalid in updated
                   !stoppedValidCurrent && // stopped invalid in current
                   stoppedValidUpdated; // stopped valid in updated

    this.testResults.push({
      test: 'API Validation Schema Updates',
      success,
      details: success ? 
        'API validation correctly updated from breakdown to stopped' : 
        'API validation schema update failed'
    });

    console.log(success ? '✅ PASS' : '❌ FAIL');
    console.log(`   🔍 Current schema: breakdown=${breakdownValidCurrent}, stopped=${stoppedValidCurrent}`);
    console.log(`   🔍 Updated schema: breakdown=${breakdownValidUpdated}, stopped=${stoppedValidUpdated}`);
    
    return success;
  }

  async testBackwardCompatibilityDuringMigration() {
    console.log('\n🧪 Testing: Backward Compatibility During Migration');

    // Test that existing breakdown trips can be queried and updated
    try {
      // Query existing stopped trips (migrated from breakdown)
      const stoppedTrips = await this.pool.query(`
        SELECT id, status FROM trip_logs WHERE status = 'stopped'
      `);

      // Test that we can update stopped trips
      if (stoppedTrips.rows.length > 0) {
        const tripId = stoppedTrips.rows[0].id;
        await this.pool.query(`
          UPDATE trip_logs SET status = 'trip_completed' WHERE id = $1
        `, [tripId]);

        // Verify update worked
        const updatedTrip = await this.pool.query(`
          SELECT status FROM trip_logs WHERE id = $1
        `, [tripId]);

        const updateSuccess = updatedTrip.rows[0].status === 'trip_completed';

        // Revert for other tests
        await this.pool.query(`
          UPDATE trip_logs SET status = 'stopped' WHERE id = $1
        `, [tripId]);

        const success = stoppedTrips.rows.length > 0 && updateSuccess;

        this.testResults.push({
          test: 'Backward Compatibility During Migration',
          success,
          details: success ? 
            `Successfully migrated and can manipulate ${stoppedTrips.rows.length} stopped trips` : 
            'Backward compatibility failed'
        });

        console.log(success ? '✅ PASS' : '❌ FAIL');
        return success;
      } else {
        this.testResults.push({
          test: 'Backward Compatibility During Migration',
          success: false,
          details: 'No stopped trips found after migration'
        });

        console.log('❌ FAIL');
        return false;
      }
    } catch (error) {
      this.testResults.push({
        test: 'Backward Compatibility During Migration',
        success: false,
        details: `Migration compatibility error: ${error.message}`
      });

      console.log('❌ FAIL');
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Trip Status Terminology Tests\n');
    
    try {
      await this.setup();
      
      const tests = [
        () => this.testDatabaseEnumUpdate(),
        () => this.testScannerLogicCompatibility(),
        () => this.testFrontendStatusDisplayUpdates(),
        () => this.testBusinessLogicCompatibility(),
        () => this.testAPIValidationSchemaUpdates(),
        () => this.testBackwardCompatibilityDuringMigration()
      ];

      let passCount = 0;
      for (const test of tests) {
        if (await test()) {
          passCount++;
        }
      }

      console.log('\n📊 Test Results Summary:');
      console.log('========================');
      this.testResults.forEach(result => {
        console.log(`${result.success ? '✅' : '❌'} ${result.test}: ${result.details}`);
      });
      
      console.log(`\n🎯 Overall: ${passCount}/${tests.length} tests passed`);
      
      if (passCount === tests.length) {
        console.log('🎉 All trip status terminology tests PASSED!');
        console.log('✅ Ready to implement breakdown → stopped migration');
        console.log('🔧 Components to update:');
        console.log('   1. Database: trip_status enum migration');
        console.log('   2. Scanner: server/routes/scanner.js');
        console.log('   3. Frontend: TripsTable.js and status displays');
        console.log('   4. Business: business-trip-monitor.js');
        console.log('   5. API: server/routes/trips.js validation');
      } else {
        console.log('⚠️  Some tests failed - review implementation before proceeding');
      }

    } catch (error) {
      console.error('❌ Test execution failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');
    await this.pool.end();
    console.log('✅ Cleanup complete');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new TripStatusTerminologyTester();
  tester.runAllTests().catch(console.error);
}

module.exports = TripStatusTerminologyTester;
